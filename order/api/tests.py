from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from decimal import Decimal

from crop.models import Crop, CropType
from wallet.models import Wallet
from ..models import Order
from payment.models import Payment

User = get_user_model()


class OrderAPITestCase(TestCase):
    """Test cases for Order API endpoints"""

    def setUp(self):
        """Set up test data"""
        # Create test users
        self.buyer = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            fullname='Test Buyer',
            phone='+1234567890',
            role='user',
            governorate='Cairo',
            address='123 Test Street',
            is_email_verified=True
        )
        
        self.farmer = User.objects.create_user(
            username='<EMAIL>',
            email='<EMAIL>',
            password='testpass123',
            fullname='Test Farmer',
            phone='+0987654321',
            role='farmer',
            governorate='Giza',
            address='456 Farm Road',
            is_email_verified=True
        )

        # Create crop type
        self.crop_type = CropType.objects.create(
            crop_name='Tomatoes',
            description='Fresh red tomatoes'
        )

        # Create test crop
        self.crop = Crop.objects.create(
            farmer=self.farmer,
            crop_type=self.crop_type,
            quantity=Decimal('100.00'),
            unit='kg',
            price=Decimal('5.50'),
            harvest_date='2025-01-15',
            description='High quality tomatoes',
            status='approved'
        )

        # Create wallet for buyer
        self.wallet = Wallet.objects.create(
            userid=self.buyer,
            balance=Decimal('500.00')
        )

        # Set up API client
        self.client = APIClient()

    def authenticate_buyer(self):
        """Authenticate as buyer"""
        self.client.force_authenticate(user=self.buyer)

    def test_get_buyer_checkout_info_success(self):
        """Test successful retrieval of buyer checkout info"""
        self.authenticate_buyer()
        url = reverse('order:buyer-checkout-info')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['fullname'], 'Test Buyer')
        self.assertEqual(response.data['data']['email'], '<EMAIL>')

    def test_get_buyer_checkout_info_unauthorized(self):
        """Test checkout info access without authentication"""
        url = reverse('order:buyer-checkout-info')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_get_buyer_checkout_info_farmer_forbidden(self):
        """Test checkout info access by farmer (should be forbidden)"""
        self.client.force_authenticate(user=self.farmer)
        url = reverse('order:buyer-checkout-info')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_order_success_cash_on_delivery(self):
        """Test successful order creation with cash on delivery"""
        self.authenticate_buyer()
        url = reverse('order:order-create')
        
        data = {
            'crop_id': self.crop.id,
            'quantity': '10.00',
            'add_engineer_check': False,
            'payment_method': 'cash_on_delivery'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['payment_method'], 'cash_on_delivery')
        
        # Verify order was created
        order = Order.objects.get(id=response.data['data']['order']['id'])
        self.assertEqual(order.buyer, self.buyer)
        self.assertEqual(order.crop, self.crop)
        self.assertEqual(order.quantity, Decimal('10.00'))

    def test_create_order_success_wallet_payment(self):
        """Test successful order creation with wallet payment"""
        self.authenticate_buyer()
        url = reverse('order:order-create')
        
        data = {
            'crop_id': self.crop.id,
            'quantity': '5.00',
            'add_engineer_check': True,
            'payment_method': 'wallet'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['payment_method'], 'wallet')
        
        # Verify wallet balance was deducted
        self.wallet.refresh_from_db()
        expected_total = Decimal('5.00') * Decimal('5.50') + Decimal('25.00') + Decimal('15.00')  # quantity * price + shipping + engineer
        expected_balance = Decimal('500.00') - expected_total
        self.assertEqual(self.wallet.balance, expected_balance)

    def test_create_order_insufficient_wallet_balance(self):
        """Test order creation with insufficient wallet balance"""
        self.authenticate_buyer()
        self.wallet.balance = Decimal('10.00')  # Set low balance
        self.wallet.save()
        
        url = reverse('order:order-create')
        data = {
            'crop_id': self.crop.id,
            'quantity': '50.00',  # Large quantity
            'payment_method': 'wallet'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('Insufficient wallet balance', response.data['message'])

    def test_create_order_insufficient_crop_quantity(self):
        """Test order creation with insufficient crop quantity"""
        self.authenticate_buyer()
        url = reverse('order:order-create')
        
        data = {
            'crop_id': self.crop.id,
            'quantity': '150.00',  # More than available (100.00)
            'payment_method': 'cash_on_delivery'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('Insufficient quantity', response.data['message'])

    def test_create_order_invalid_crop_id(self):
        """Test order creation with invalid crop ID"""
        self.authenticate_buyer()
        url = reverse('order:order-create')
        
        data = {
            'crop_id': 99999,  # Non-existent crop
            'quantity': '10.00',
            'payment_method': 'cash_on_delivery'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])

    def test_create_order_unauthorized(self):
        """Test order creation without authentication"""
        url = reverse('order:order-create')
        
        data = {
            'crop_id': self.crop.id,
            'quantity': '10.00',
            'payment_method': 'cash_on_delivery'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_create_order_farmer_forbidden(self):
        """Test order creation by farmer (should be forbidden)"""
        self.client.force_authenticate(user=self.farmer)
        url = reverse('order:order-create')
        
        data = {
            'crop_id': self.crop.id,
            'quantity': '10.00',
            'payment_method': 'cash_on_delivery'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
