# API Usage Examples

## Authentication
First, you need to obtain a JWT token by logging in:

```bash
curl -X POST http://localhost:8000/api/account/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password"
  }'
```

Response:
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "username": "<EMAIL>",
    "email": "<EMAIL>",
    "fullname": "<PERSON>",
    "phone": "+**********",
    "role": "user",
    "is_superuser": false
  }
}
```

Use the `access` token in subsequent requests.

## 1. Get Buyer Checkout Information

```bash
curl -X GET http://localhost:8000/api/order/buyer/checkout/info/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "message": "Buyer checkout information retrieved successfully.",
  "data": {
    "id": 1,
    "fullname": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "governorate": "Cairo",
    "address": "123 Main Street"
  }
}
```

## 2. Create Order - Cash on Delivery

```bash
curl -X POST http://localhost:8000/api/order/buyer/checkout/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "crop_id": 1,
    "quantity": 10.5,
    "delivery_address": "456 Custom Delivery Address",
    "add_engineer_check": false,
    "payment_method": "cash_on_delivery"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Order created successfully.",
  "data": {
    "order": {
      "id": 123,
      "crop_name": "Tomatoes",
      "crop_unit": "kg",
      "crop_price_per_unit": "5.50",
      "quantity": "10.50",
      "total_amount": "82.75",
      "shipping_fee": "25.00",
      "add_engineer_check": false,
      "status": "pending_admin",
      "farmer_name": "Ahmed Hassan",
      "farmer_phone": "+20**********",
      "buyer_name": "John Doe",
      "buyer_phone": "+**********",
      "estimated_delivery_days": 3,
      "created_at": "2025-01-08T10:30:00Z"
    },
    "payment_method": "cash_on_delivery",
    "payment_status": "held",
    "delivery_address": "456 Custom Delivery Address"
  }
}
```

## 3. Create Order - Wallet Payment with Engineer Check

```bash
curl -X POST http://localhost:8000/api/order/buyer/checkout/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "crop_id": 2,
    "quantity": 5.0,
    "add_engineer_check": true,
    "payment_method": "wallet"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Order created successfully.",
  "data": {
    "order": {
      "id": 124,
      "crop_name": "Oranges",
      "crop_unit": "kg",
      "crop_price_per_unit": "8.00",
      "quantity": "5.00",
      "total_amount": "90.00",
      "shipping_fee": "10.00",
      "add_engineer_check": true,
      "status": "pending_admin",
      "farmer_name": "Mohamed Ali",
      "farmer_phone": "+201987654321",
      "buyer_name": "John Doe",
      "buyer_phone": "+**********",
      "estimated_delivery_days": 3,
      "created_at": "2025-01-08T10:35:00Z"
    },
    "payment_method": "wallet",
    "payment_status": "held",
    "delivery_address": "123 Main Street"
  }
}
```

## Error Examples

### 1. Insufficient Wallet Balance
```bash
curl -X POST http://localhost:8000/api/order/buyer/checkout/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "crop_id": 1,
    "quantity": 100.0,
    "payment_method": "wallet"
  }'
```

**Response:**
```json
{
  "success": false,
  "message": "Insufficient wallet balance. Required: 575.00, Available: 250.00"
}
```

### 2. Insufficient Crop Quantity
```bash
curl -X POST http://localhost:8000/api/order/buyer/checkout/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "crop_id": 1,
    "quantity": 1000.0,
    "payment_method": "cash_on_delivery"
  }'
```

**Response:**
```json
{
  "success": false,
  "message": "Insufficient quantity. Available: 50.00, Requested: 1000.00"
}
```

### 3. Invalid Crop ID
```bash
curl -X POST http://localhost:8000/api/order/buyer/checkout/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "crop_id": 99999,
    "quantity": 10.0,
    "payment_method": "cash_on_delivery"
  }'
```

**Response:**
```json
{
  "success": false,
  "message": "Invalid order data.",
  "errors": {
    "crop_id": ["Crop not found."]
  }
}
```

### 4. Unauthorized Access (No Token)
```bash
curl -X GET http://localhost:8000/api/order/buyer/checkout/info/ \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 5. Forbidden Access (Farmer trying to buy)
```bash
curl -X GET http://localhost:8000/api/order/buyer/checkout/info/ \
  -H "Authorization: Bearer FARMER_ACCESS_TOKEN" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "error": "Only users with buyer privileges can access checkout information."
}
```

## JavaScript/Frontend Example

```javascript
// Get checkout info
async function getBuyerCheckoutInfo() {
  const response = await fetch('/api/order/buyer/checkout/info/', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json',
    },
  });
  
  const data = await response.json();
  return data;
}

// Create order
async function createOrder(orderData) {
  const response = await fetch('/api/order/buyer/checkout/', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(orderData),
  });
  
  const data = await response.json();
  return data;
}

// Usage
const checkoutInfo = await getBuyerCheckoutInfo();
console.log('Buyer info:', checkoutInfo.data);

const orderResult = await createOrder({
  crop_id: 1,
  quantity: 10.5,
  add_engineer_check: true,
  payment_method: 'wallet'
});
console.log('Order created:', orderResult.data);
```
