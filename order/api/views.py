from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.db import transaction
from django.utils import timezone
from decimal import Decimal
from django.contrib.auth import get_user_model

from .serializers import (
    BuyerCheckoutInfoSerializer,
    OrderCreateSerializer,
    OrderConfirmationSerializer
)
from ..models import Order
from crop.models import Crop
from payment.models import Payment
from wallet.models import Wallet, WalletTransactions

User = get_user_model()


class BuyerCheckoutInfoAPIView(APIView):
    """
    Get buyer checkout information
    URL: /api/order/buyer/checkout/info/
    Method: GET
    Authentication: Required (buyer role only)
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Check if user has buyer role (user or admin can also buy)
        if request.user.role not in ['user', 'admin']:
            return Response(
                {"error": "Only users with buyer privileges can access checkout information."},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = BuyerCheckoutInfoSerializer(request.user)
        
        return Response({
            "success": True,
            "message": "Buyer checkout information retrieved successfully.",
            "data": serializer.data
        }, status=status.HTTP_200_OK)


class OrderCreateAPIView(APIView):
    """
    Create/Confirm Order
    URL: /api/order/buyer/checkout/
    Method: POST
    Authentication: Required (buyer role only)
    """
    permission_classes = [IsAuthenticated]

    def calculate_shipping_fee(self, buyer_governorate, crop_governorate):
        """
        Calculate shipping fee based on governorates
        This is a simplified calculation - can be enhanced with actual shipping logic
        """
        if buyer_governorate.lower() == crop_governorate.lower():
            return Decimal('10.00')  # Same governorate
        else:
            return Decimal('25.00')  # Different governorate

    def calculate_engineer_fee(self):
        """Calculate engineer check fee"""
        return Decimal('15.00')  # Fixed engineer check fee

    def post(self, request):
        # Check if user has buyer role
        if request.user.role not in ['user', 'admin']:
            return Response(
                {"error": "Only users with buyer privileges can create orders."},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = OrderCreateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                "success": False,
                "message": "Invalid order data.",
                "errors": serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        validated_data = serializer.validated_data
        
        try:
            with transaction.atomic():
                # Get the crop
                crop = Crop.objects.select_for_update().get(
                    id=validated_data['crop_id'],
                    is_active=True,
                    is_deleted=False
                )
                
                # Check if crop is still available
                if crop.status != 'approved':
                    return Response({
                        "success": False,
                        "message": "This crop is no longer available for purchase."
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # Check quantity availability
                if validated_data['quantity'] > crop.quantity:
                    return Response({
                        "success": False,
                        "message": f"Insufficient quantity. Available: {crop.quantity}, Requested: {validated_data['quantity']}"
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Calculate amounts
                quantity = validated_data['quantity']
                unit_price = crop.price
                subtotal = quantity * unit_price
                
                # Calculate shipping fee
                delivery_address = validated_data.get('delivery_address', request.user.address)
                buyer_governorate = request.user.governorate
                farmer_governorate = crop.farmer.governorate
                shipping_fee = self.calculate_shipping_fee(buyer_governorate, farmer_governorate)
                
                # Add engineer check fee if requested
                engineer_fee = Decimal('0.00')
                if validated_data.get('add_engineer_check', False):
                    engineer_fee = self.calculate_engineer_fee()
                
                total_amount = subtotal + shipping_fee + engineer_fee

                # Check wallet balance if payment method is wallet
                payment_method = validated_data.get('payment_method', 'cash_on_delivery')
                if payment_method == 'wallet':
                    try:
                        wallet = Wallet.objects.get(userid=request.user, is_active=True, is_deleted=False)
                        if wallet.balance < total_amount:
                            return Response({
                                "success": False,
                                "message": f"Insufficient wallet balance. Required: {total_amount}, Available: {wallet.balance}"
                            }, status=status.HTTP_400_BAD_REQUEST)
                    except Wallet.DoesNotExist:
                        return Response({
                            "success": False,
                            "message": "Wallet not found. Please create a wallet first."
                        }, status=status.HTTP_400_BAD_REQUEST)

                # Create the order
                order = Order.objects.create(
                    crop=crop,
                    buyer=request.user,
                    quantity=quantity,
                    total_amount=total_amount,
                    shipping_fee=shipping_fee,
                    add_engineer_check=validated_data.get('add_engineer_check', False),
                    status='pending_admin'
                )

                # Create payment record
                payment = Payment.objects.create(
                    order=order,
                    amount=total_amount,
                    payment_status='held',
                    hold_date=timezone.now()
                )

                # Handle wallet payment
                if payment_method == 'wallet':
                    # Deduct from wallet and create transaction
                    wallet = Wallet.objects.get(userid=request.user)
                    balance_before = wallet.balance
                    wallet.balance -= total_amount
                    wallet.save()

                    # Create wallet transaction
                    WalletTransactions.objects.create(
                        walletid=wallet,
                        userid=request.user,
                        amount=total_amount,
                        transaction_type='payment',
                        related_orderid=order,
                        related_paymentid=payment,
                        description=f"Payment for order #{order.id} - {crop.crop_type.crop_name}",
                        balance_before=balance_before,
                        balance_after=wallet.balance,
                        status='completed'
                    )

                # Update crop quantity
                crop.quantity -= quantity
                if crop.quantity == 0:
                    crop.status = 'sold'
                crop.save()

                # Serialize the order for response
                order_serializer = OrderConfirmationSerializer(order)
                
                return Response({
                    "success": True,
                    "message": "Order created successfully.",
                    "data": {
                        "order": order_serializer.data,
                        "payment_method": payment_method,
                        "payment_status": payment.payment_status,
                        "delivery_address": delivery_address,
                    }
                }, status=status.HTTP_201_CREATED)

        except Crop.DoesNotExist:
            return Response({
                "success": False,
                "message": "Crop not found."
            }, status=status.HTTP_404_NOT_FOUND)
        
        except Exception as e:
            return Response({
                "success": False,
                "message": f"An error occurred while creating the order: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
