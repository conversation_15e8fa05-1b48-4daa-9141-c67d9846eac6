from rest_framework import serializers
from django.contrib.auth import get_user_model
from decimal import Decimal
from ..models import Order, OrderImage
from crop.models import Crop
from payment.models import Payment
from wallet.models import Wallet

User = get_user_model()


class BuyerCheckoutInfoSerializer(serializers.ModelSerializer):
    """Serializer for buyer checkout information"""
    
    class Meta:
        model = User
        fields = [
            'id',
            'fullname',
            'email',
            'phone',
            'governorate',
            'address',
        ]
        read_only_fields = ['id', 'fullname', 'email', 'phone']


class OrderCreateSerializer(serializers.Serializer):
    """Serializer for creating a new order"""
    
    crop_id = serializers.IntegerField()
    quantity = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=0.01)
    delivery_address = serializers.CharField(max_length=255, required=False, allow_blank=True)
    add_engineer_check = serializers.BooleanField(default=False)
    payment_method = serializers.ChoiceField(
        choices=[
            ('wallet', 'Wallet'),
            ('cash_on_delivery', 'Cash on Delivery'),
        ],
        default='cash_on_delivery'
    )

    def validate_crop_id(self, value):
        """Validate that the crop exists and is available for purchase"""
        try:
            crop = Crop.objects.get(id=value, is_active=True, is_deleted=False)
            if crop.status != 'approved':
                raise serializers.ValidationError("This crop is not available for purchase.")
            return value
        except Crop.DoesNotExist:
            raise serializers.ValidationError("Crop not found.")

    def validate_quantity(self, value):
        """Validate that the quantity is positive"""
        if value <= 0:
            raise serializers.ValidationError("Quantity must be greater than 0.")
        return value

    def validate(self, attrs):
        """Cross-field validation"""
        crop_id = attrs.get('crop_id')
        quantity = attrs.get('quantity')
        
        try:
            crop = Crop.objects.get(id=crop_id)
            if quantity > crop.quantity:
                raise serializers.ValidationError({
                    'quantity': f"Requested quantity ({quantity}) exceeds available quantity ({crop.quantity})."
                })
        except Crop.DoesNotExist:
            pass  # This will be caught by crop_id validation
            
        return attrs


class OrderConfirmationSerializer(serializers.ModelSerializer):
    """Serializer for order confirmation response"""
    
    crop_name = serializers.CharField(source='crop.crop_type.crop_name', read_only=True)
    crop_unit = serializers.CharField(source='crop.unit', read_only=True)
    crop_price_per_unit = serializers.DecimalField(source='crop.price', max_digits=10, decimal_places=2, read_only=True)
    farmer_name = serializers.CharField(source='crop.farmer.fullname', read_only=True)
    farmer_phone = serializers.CharField(source='crop.farmer.phone', read_only=True)
    buyer_name = serializers.CharField(source='buyer.fullname', read_only=True)
    buyer_phone = serializers.CharField(source='buyer.phone', read_only=True)
    estimated_delivery_days = serializers.SerializerMethodField()
    
    class Meta:
        model = Order
        fields = [
            'id',
            'crop_name',
            'crop_unit',
            'crop_price_per_unit',
            'quantity',
            'total_amount',
            'shipping_fee',
            'add_engineer_check',
            'status',
            'farmer_name',
            'farmer_phone',
            'buyer_name',
            'buyer_phone',
            'estimated_delivery_days',
            'created_at',
        ]
        read_only_fields = ['id', 'created_at']

    def get_estimated_delivery_days(self, obj):
        """Calculate estimated delivery days based on location"""
        # Simple logic - can be enhanced based on actual shipping rules
        return 3  # Default 3 days


class OrderSerializer(serializers.ModelSerializer):
    """General order serializer"""
    
    class Meta:
        model = Order
        fields = '__all__'


class OrderImageSerializer(serializers.ModelSerializer):
    """Serializer for order images"""
    
    class Meta:
        model = OrderImage
        fields = '__all__'
