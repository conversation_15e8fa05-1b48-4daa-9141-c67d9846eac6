# Order API Documentation

## Overview
This module implements the checkout functionality for the Mahsool agricultural marketplace platform. It provides two main API endpoints for buyers to retrieve their checkout information and create orders.

## API Endpoints

### 1. Get Buyer Checkout Information
**Endpoint:** `GET /api/order/buyer/checkout/info/`
**Authentication:** Required (JW<PERSON>)
**Permissions:** User role must be 'user' or 'admin'

**Description:** Retrieves the authenticated buyer's delivery information for checkout.

**Response Format:**
```json
{
    "success": true,
    "message": "Buyer checkout information retrieved successfully.",
    "data": {
        "id": 1,
        "fullname": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "governorate": "Cairo",
        "address": "123 Main Street"
    }
}
```

### 2. Create/Confirm Order
**Endpoint:** `POST /api/order/buyer/checkout/`
**Authentication:** Required (JW<PERSON>)
**Permissions:** User role must be 'user' or 'admin'

**Description:** Creates a new order with the specified items and delivery details.

**Request Body:**
```json
{
    "crop_id": 1,
    "quantity": 10.50,
    "delivery_address": "456 Delivery Street (optional)",
    "add_engineer_check": true,
    "payment_method": "wallet"
}
```

**Request Parameters:**
- `crop_id` (integer, required): ID of the crop being ordered
- `quantity` (decimal, required): Quantity to order (must be > 0)
- `delivery_address` (string, optional): Custom delivery address (defaults to user's address)
- `add_engineer_check` (boolean, optional): Whether to add engineer quality check (default: false)
- `payment_method` (string, optional): Payment method - "wallet" or "cash_on_delivery" (default: "cash_on_delivery")

**Response Format:**
```json
{
    "success": true,
    "message": "Order created successfully.",
    "data": {
        "order": {
            "id": 123,
            "crop_name": "Tomatoes",
            "crop_unit": "kg",
            "crop_price_per_unit": "5.50",
            "quantity": "10.50",
            "total_amount": "82.75",
            "shipping_fee": "25.00",
            "add_engineer_check": true,
            "status": "pending_admin",
            "farmer_name": "Ahmed Hassan",
            "farmer_phone": "+201234567890",
            "buyer_name": "John Doe",
            "buyer_phone": "+1234567890",
            "estimated_delivery_days": 3,
            "created_at": "2025-01-08T10:30:00Z"
        },
        "payment_method": "wallet",
        "payment_status": "held",
        "delivery_address": "456 Delivery Street"
    }
}
```

## Business Logic

### Order Creation Process
1. **Validation**: Validates crop availability, quantity, and user permissions
2. **Calculation**: Calculates total amount including shipping and engineer fees
3. **Payment Processing**: Handles wallet payment or sets up cash on delivery
4. **Inventory Update**: Reduces crop quantity and updates status if sold out
5. **Transaction Recording**: Creates order, payment, and wallet transaction records

### Pricing Calculation
- **Subtotal**: quantity × crop.price
- **Shipping Fee**: 
  - Same governorate: 10.00 EGP
  - Different governorate: 25.00 EGP
- **Engineer Check Fee**: 15.00 EGP (if requested)
- **Total**: subtotal + shipping_fee + engineer_fee

### Payment Methods
1. **Wallet Payment**: 
   - Validates sufficient balance
   - Deducts amount immediately
   - Creates wallet transaction record
   - Sets payment status to 'held'

2. **Cash on Delivery**:
   - No immediate payment processing
   - Sets payment status to 'held'
   - Payment collected upon delivery

### Order Status Flow
1. **pending_admin**: Initial status after order creation
2. **shipping**: Admin approves and assigns to shipping
3. **awaiting_confirmation**: Package delivered, awaiting buyer confirmation
4. **under_review**: Quality issues reported, under investigation
5. **completed**: Order successfully completed
6. **rejected**: Order rejected by admin or buyer

## Error Handling

### Common Error Responses
- **403 Forbidden**: User doesn't have buyer privileges
- **400 Bad Request**: Invalid input data or insufficient resources
- **404 Not Found**: Crop not found
- **500 Internal Server Error**: Unexpected server error

### Validation Errors
- Crop not found or not available
- Insufficient crop quantity
- Invalid quantity (must be > 0)
- Insufficient wallet balance (for wallet payments)
- Wallet not found (for wallet payments)

## Security Features
- JWT authentication required
- Role-based access control (only users and admins can buy)
- Atomic transactions to prevent race conditions
- Input validation and sanitization
- Balance verification for wallet payments

## Database Models Used
- **Order**: Main order record
- **Payment**: Payment tracking
- **Crop**: Product information
- **User**: Buyer and farmer information
- **Wallet**: User wallet balance
- **WalletTransactions**: Payment transaction history

## Dependencies
- Django REST Framework
- Django ORM with PostgreSQL
- JWT Authentication (djangorestframework-simplejwt)
- Atomic transactions for data consistency
